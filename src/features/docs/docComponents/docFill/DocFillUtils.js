import { getFormFieldConversionsFromDb } from "../../../../app/firestore/firestoreService";
import {
  convertAddressCity,
  convertAddressCityStateZipcode,
  convertAddressFull,
  convertAddressState,
  convertAddressStreet,
  convertAddressStreetAndUnit,
  convertAddressZipcode,
  convertFullName,
  convertPartyLegalName,
  convertTransactionLegalNames,
  createPoaSignatureName,
} from "../../../../app/common/util/util";
import _ from "lodash";
import { format } from "date-fns";

// function getCalculationForFieldPopulate(calcString) {
//   if (!calcString) return "";
//   console.log("get calc string: ", calcString);
//   calcString.split("_").forEach(token => {
//     console.log("token = ", token);

//   });
//   return "010";
// }

export async function docPrepopulateFields(doc, parties, transaction) {
  let formFieldValues = {};
  if (!doc.fieldsPopulated && doc.hasFieldConversions) {
    const fieldConversions = await getFormFieldConversionsFromDb(
      doc.formId,
      transaction
    );
    let convertedParties = convertPartyInfo(parties);

    fieldConversions.forEach((item) => {
      if (convertedParties[item.convertTo?.split("_")?.[0]]) {
        let value = "";
        value =
          convertedParties[item.convertTo.split("_")[0]][
            item.convertTo.split("_")[1]
          ];
        if (value) {
          formFieldValues[item.pdfFieldName] = value;
        }
      }
      // if (item.convertTo?.split("_")?.[0] === "calc") {
      //   console.log("do calc ", item.convertTo?.split("_")?.[1]);
      //   formFieldValues[item.pdfFieldName] = getCalculationForFieldPopulate(item.convertTo?.slice(item.convertTo?.indexOf('_')+1));
      // }
    });
  }
  return formFieldValues;
}


export function convertPartyInfo(parties) {
  let partiesCopy = _.cloneDeep(parties);
  let convertedParty = {};

  partiesCopy.forEach((party) => {
    if (party.lastName) {
      party.fullname = convertFullName(party);
    }
    if (!_.isEmpty(party.address)) {
      party.addressFull = convertAddressFull(party.address);
      party.addressStreet = convertAddressStreet(party.address);
      party.addressStreetAndUnit = convertAddressStreetAndUnit(party.address);
      party.addressCity = convertAddressCity(party.address);
      party.addressState = convertAddressState(party.address);
      party.addressZipcode = convertAddressZipcode(party.address);
      party.addressCityStateZipcode = convertAddressCityStateZipcode(
        party.address
      );
    }
    if (party.role === "Buyer") {
      party.legalName = convertPartyLegalName(party);
      party.signerFullName = party.hasPoa
        ? createPoaSignatureName(party, party.poaDisplayOption)
        : convertFullName(party);
      convertedParty.buyer = party;
    }
    else if (party.role === "Buyer 2") {
      party.legalName = convertPartyLegalName(party);
      party.signerFullName = party.hasPoa
        ? createPoaSignatureName(party, party.poaDisplayOption)
        : convertFullName(party);
      convertedParty.buyer2 = party;
    }
    else if (party.role === "Buyer 3") {
      party.legalName = convertPartyLegalName(party);
      party.signerFullName = party.hasPoa
        ? createPoaSignatureName(party, party.poaDisplayOption)
        : convertFullName(party);
      convertedParty.buyer3 = party;
    }
    else if (party.role === "Seller") {
      party.legalName = convertPartyLegalName(party);
      party.signerFullName = party.hasPoa
        ? createPoaSignatureName(party, party.poaDisplayOption)
        : convertFullName(party);
      convertedParty.seller = party;
    }
    else if (party.role === "Seller 2") {
      party.legalName = convertPartyLegalName(party);
      party.signerFullName = party.hasPoa
        ? createPoaSignatureName(party, party.poaDisplayOption)
        : convertFullName(party);
      convertedParty.seller2 = party;
    }
    else if (party.role === "Seller 3") {
      party.legalName = convertPartyLegalName(party);
      party.signerFullName = party.hasPoa
        ? createPoaSignatureName(party, party.poaDisplayOption)
        : convertFullName(party);
      convertedParty.seller3 = party;
    }
    else if (party.role === "Buyer Agent") {
      convertedParty.brokerBuyer = party;
    }
    else if (party.role === "Listing Agent") {
      convertedParty.brokerSeller = party;
    }
    else if (party.role === "Title Company") {
      convertedParty.titleCompany = party;
    }
    else if (party.role === "CoAgent (Mine)") {
      convertedParty.coAgent = party;
    }

    else if (party.role === "Transaction") {
      const transactionLegalNames = convertTransactionLegalNames(parties);
      party.buyerLegalName = transactionLegalNames?.buyer;
      party.sellerLegalName = transactionLegalNames?.seller;
      party.addressCounty = party.propertyDetails?.county;
      party.addressLegal = party.propertyDetails?.legalDescription;
      party.inclusions = party.propertyDetails?.inclusions;
      party.exclusions = party.propertyDetails?.exclusions;
      party.yearBuilt = party.propertyDetails?.yearBuilt;
      party.brokerageLogoRef = party.agentProfile?.brokerageLogoRef || "";
      party.contractDateText =
        (party.contractDateTime &&
          format(party.contractDateTime, "M/d/yyyy")) ||
        "";
      party.closingDateText =
        (party.closingDateTime &&
          format(party.closingDateTime, "M/d/yyyy")) ||
        "";
      party.currentDate = new Date();
      party.checked = true;
      party.checkedText = "X";
      party.text1 = party.cda?.text1 || "";
      party.text2 = party.cda?.text2 || "";
      party.text3 = party.cda?.text3 || "";
      party.text4 = party.cda?.text4 || "";
      party.text5 = party.cda?.text5 || "";
      party.text6 = party.cda?.text6 || "";
      party.text7 = party.cda?.text7 || "";
      party.text8 = party.cda?.text8 || "";
      party.text9 = party.cda?.text9 || "";
      party.text10 = party.cda?.text10 || "";
      party.grossCompensation = party.cda?.compensationPayAmount || "";
      party.cdaSpecialInstructions = party.cda?.specialInstructions || "";

      convertedParty.transaction = party;

     
    }
    if (convertedParty?.transaction?.agentRepresents === "Buyer") {
      convertedParty.transaction.clientFullNames = convertedParty.transaction.buyerLegalName;
      convertedParty.transaction.client1FullName = convertedParty.buyer.legalName;
      convertedParty.transaction.client2FullName = (convertedParty.buyer2 ? convertedParty.buyer2.legalName : "");
      convertedParty.transaction.client3FullName = (convertedParty.buyer3 ? convertedParty.buyer3.legalName : "");
      convertedParty.transaction.client1Email = convertedParty.buyer.email;
      convertedParty.transaction.client2Email = (convertedParty.buyer2 ? convertedParty.buyer2.email : "");
      convertedParty.transaction.client3Email = (convertedParty.buyer3 ? convertedParty.buyer3.email : "");
      convertedParty.transaction.client1SignerFullName = convertedParty.buyer.signerFullName || convertedParty.buyer.legalName;
      convertedParty.transaction.client2SignerFullName = (convertedParty.buyer2?.signerFullName ? convertedParty.buyer2.signerFullName : (convertedParty.buyer2?.legalName || "")); 
      convertedParty.transaction.client3SignerFullName = (convertedParty.buyer3?.signerFullName ? convertedParty.buyer3.signerFullName : (convertedParty.buyer3?.legalName || "")); 
      convertedParty.meAgent = convertedParty.brokerBuyer;
      const coAgentMine = partiesCopy?.filter((oneParty) => oneParty.role === "CoAgent (Mine)");
      if (coAgentMine && coAgentMine[0]) {
        convertedParty.coAgentBuyer = coAgentMine[0];
      }
  } 
  else if (convertedParty?.transaction?.agentRepresents === "Seller") {
    convertedParty.transaction.clientFullNames = convertedParty.transaction.sellerLegalName;
      convertedParty.transaction.client1FullName = convertedParty.seller.legalName;
      convertedParty.transaction.client2FullName = (convertedParty.seller2 ? convertedParty.seller2.legalName : "");
      convertedParty.transaction.client3FullName = (convertedParty.seller3 ? convertedParty.seller3.legalName : "");
      convertedParty.transaction.client1SignerFullName = convertedParty.seller.signerFullName || convertedParty.seller.legalName;
      convertedParty.transaction.client2SignerFullName = (convertedParty.seller2?.signerFullName ? convertedParty.seller2.signerFullName : (convertedParty.seller2?.legalName || "")); 
      convertedParty.transaction.client3SignerFullName = (convertedParty.seller3?.signerFullName ? convertedParty.seller3.signerFullName : (convertedParty.seller3?.legalName || "")); 
      convertedParty.transaction.client1Email = convertedParty.seller.email;
      convertedParty.transaction.client2Email = (convertedParty.seller2 ? convertedParty.seller2.email : "");
      convertedParty.transaction.client3Email = (convertedParty.seller3 ? convertedParty.seller3.email : "");
    convertedParty.meAgent = convertedParty.brokerSeller;
      const coAgentMine = partiesCopy?.filter((oneParty) => oneParty.role === "CoAgent (Mine)");
      if (coAgentMine && coAgentMine[0]) {
        convertedParty.coAgentSeller = coAgentMine[0];
      }
  } 
    });
  
  return convertedParty;
}
