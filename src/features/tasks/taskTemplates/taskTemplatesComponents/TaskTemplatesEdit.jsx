import { Formik, Form } from "formik";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  <PERSON><PERSON>,
  Divider,
  Grid,
  Header,
  Segment,
  Table,
  Modal,
  Popup,
  Icon,
} from "semantic-ui-react";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useMediaQuery } from "react-responsive";
import { closeModal } from "../../../../app/common/modals/modalSlice";
import ModalWrapper from "../../../../app/common/modals/modalWrapper";
import MyTextInput from "../../../../app/common/form/MyTextInput";
import MyNumberInput from "../../../../app/common/form/MyNumberInput";
import MyTextArea from "../../../../app/common/form/MyTextArea";
import MySelectInput from "../../../../app/common/form/MySelectInput";
import MyCheckbox from "../../../../app/common/form/MyCheckbox";
import {
  addItemToTaskTemplate,
  updateItemInTaskTemplate,
  updateTaskTemplateTitle,
  updateTaskTemplateAutoAdd,
  moveItemInTaskTemplate,
} from "../taskTemplatesSlice";
import {
  addTaskTemplateToDb,
  updateTaskTemplateInDb,
} from "../../../../app/firestore/firestoreService";
import TaskTemplatesItemActionButtons from "../TaskTemplatesItemActionButtons";

export default function TaskTemplatesEdit({ newTemplate = false }) {
  const dispatch = useDispatch();
  const { taskTemplate } = useSelector((state) => state.taskTemplates);
  const { currentUserProfile } = useSelector((state) => state.profile);

  const isMobile = useMediaQuery({ query: "(max-width:768px)" });
  const [itemModal, setItemModal] = useState({});
  const [assigneeOptions, setAssigneeOptions] = useState([]);

  const [initialValues, setInitialValues] = useState({
    name: "",
    description: "",
    days: "",
    beforeOnAfter: "",
    dateType: "",
    assignedTo: [],
    visibleTo: [],
  });

  const initialValuesTemplate = taskTemplate
    ? taskTemplate
    : {
        title: "",
        items: [],
        autoAddToBuyerTransactions: false,
        autoAddToSellerTransactions: false,
      };

  const validationSchemaTemplate = Yup.object({
    title: Yup.string().required("You must provide a name"),
  });

  const validationSchema = Yup.object({
    name: Yup.string().required("You must provide a name"),
  });

  // Generate assignee options based on current user profile
  useEffect(() => {
    let options = [
      { key: "", text: "", value: "" },
      { key: "agent", text: "Agent", value: "Agent" },
      { key: "coAgent", text: "Co-Agent", value: "Co-Agent" },
      {
        key: "tc",
        text: "Transaction Coordinator",
        value: "Transaction Coordinator",
      },
      { key: "manager", text: "Manager", value: "Manager" },
    ];

    // Add Manager Assistants if they exist
    if (
      currentUserProfile?.managerAssistants &&
      currentUserProfile.managerAssistants.length > 0
    ) {
      currentUserProfile.managerAssistants.forEach((assistant, index) => {
        if (assistant.userId) {
          options.push({
            key: `managerassistant-${index}`,
            value: `ManagerAssistant-${assistant.userId}`,
            text: assistant.title || "Manager Assistant",
          });
        }
      });
    }

    // Add the current user if they are a Manager Assistant
    if (
      currentUserProfile?.role === "managerassistant" &&
      currentUserProfile?.userId &&
      currentUserProfile?.authCustomClaims?.m
    ) {
      options.push({
        key: "currentManagerAssistant",
        // value: currentUserProfile.userId,
        value: `${currentUserProfile.roleTitle || "Manager Assistant"}: ${
          currentUserProfile.firstName || ""
        } ${currentUserProfile.lastName || ""}`,
        text: `${currentUserProfile.roleTitle || "Manager Assistant"}: ${
          currentUserProfile.firstName || ""
        } ${currentUserProfile.lastName || ""}`,
      });
    }

    setAssigneeOptions(options || []);
  }, [currentUserProfile]);

  function handleAddItem() {
    setInitialValues({
      name: "",
      description: "",
      days: "",
      beforeOnAfter: "",
      dateType: "",
      assignedTo: [],
      visibleTo: [],
    });
    setItemModal({ open: true, new: true });
  }

  function handleMoveUp(index) {
    if (index > 0) {
      dispatch(
        moveItemInTaskTemplate({ fromIndex: index, toIndex: index - 1 })
      );
    }
  }

  function handleMoveDown(index) {
    if (index < taskTemplate.items.length - 1) {
      dispatch(
        moveItemInTaskTemplate({ fromIndex: index, toIndex: index + 1 })
      );
    }
  }

  // Function to get display text for assignedTo in the table
  function getAssignedToDisplayText(assignedTo) {
    if (!assignedTo || (Array.isArray(assignedTo) && assignedTo.length === 0))
      return "";

    return assignedTo
      .map((assignee) => {
        // Check if it's a Manager Assistant
        if (assignee.startsWith("ManagerAssistant-")) {
          const assistantId = assignee.replace("ManagerAssistant-", "");
          const assistant = currentUserProfile?.managerAssistants?.find(
            (a) => a.userId === assistantId
          );
          return assistant
            ? assistant.title || "Manager Assistant"
            : "Manager Assistant";
        }

        return assignee;
      })
      .join(", ");
  }

  return (
    <>
      <ModalWrapper size="large">
        <Segment clearing>
          <div className="medium horizontal margin small top margin">
            <Header size="large" color="blue">
              Create Task Template
            </Header>
            <Divider />
            <Formik
              enableReinitialize
              initialValues={initialValuesTemplate}
              validationSchema={validationSchemaTemplate}
              validateOnChange={false}
              validateOnBlur={false}
              onSubmit={async (values, { setSubmitting }) => {
                try {
                  newTemplate
                    ? addTaskTemplateToDb(values)
                    : updateTaskTemplateInDb(values);
                  toast.success("Task template successfully updated");
                  dispatch(
                    closeModal({
                      modalType: "TaskTemplatesEdit",
                    })
                  );
                } catch (error) {
                  toast.error(error.message);
                }
              }}
            >
              {({ isSubmitting, dirty, isValid, values }) => (
                <Form className="ui form" autoComplete="off">
                  <Grid>
                    <Grid.Row className="small vertical padding">
                      <Grid.Column mobile={16} computer={6}>
                        <Header
                          as="h5"
                          color="blue"
                          className="mini bottom margin"
                        >
                          Template Name
                        </Header>
                        <MyTextInput
                          name="title"
                          onBlur={() =>
                            dispatch(updateTaskTemplateTitle(values.title))
                          }
                        />
                      </Grid.Column>
                      <Grid.Column mobile={16} computer={10}>
                        <Header
                          as="h5"
                          color="blue"
                          className="mini bottom margin"
                        >
                          Auto-add Settings
                        </Header>
                        <MyCheckbox
                          name="autoAddToBuyerTransactions"
                          label="Auto-add to new Buyer transactions"
                          onChange={(e) => {
                            dispatch(
                              updateTaskTemplateAutoAdd({
                                autoAddToBuyerTransactions:
                                  e.target.checked || false,
                                autoAddToSellerTransactions:
                                  values.autoAddToSellerTransactions || false,
                              })
                            );
                          }}
                        />
                        <MyCheckbox
                          name="autoAddToSellerTransactions"
                          label="Auto-add to new Seller transactions"
                          onChange={(e) => {
                            dispatch(
                              updateTaskTemplateAutoAdd({
                                autoAddToBuyerTransactions:
                                  values.autoAddToBuyerTransactions || false,
                                autoAddToSellerTransactions:
                                  e.target.checked || false,
                              })
                            );
                          }}
                        />
                      </Grid.Column>
                    </Grid.Row>
                  </Grid>
                  <Table>
                    <Table.Header className="white-blue-table-header">
                      <Table.Row>
                        <Table.HeaderCell>Order</Table.HeaderCell>
                        <Table.HeaderCell>Name</Table.HeaderCell>
                        <Table.HeaderCell>Days</Table.HeaderCell>
                        <Table.HeaderCell>Before/On/After</Table.HeaderCell>
                        <Table.HeaderCell>Reference Date</Table.HeaderCell>
                        <Table.HeaderCell>Assigned To</Table.HeaderCell>
                        <Table.HeaderCell></Table.HeaderCell>
                      </Table.Row>
                    </Table.Header>
                    <Table.Body>
                      {taskTemplate?.items &&
                        taskTemplate?.items?.length > 0 &&
                        taskTemplate.items.map((item, index) => (
                          <Table.Row key={item.name + index}>
                            <>
                              <Table.Cell>
                                <Button.Group size="mini">
                                  <Button
                                    type="button"
                                    icon="chevron up"
                                    disabled={index === 0}
                                    onClick={() => handleMoveUp(index)}
                                  />
                                  <Button
                                    type="button"
                                    icon="chevron down"
                                    disabled={
                                      index === taskTemplate.items.length - 1
                                    }
                                    onClick={() => handleMoveDown(index)}
                                  />
                                </Button.Group>
                              </Table.Cell>
                              <Table.Cell>
                                {item.name}
                                {item.description && (
                                  <Popup
                                    size="small"
                                    trigger={
                                      <Icon
                                        name="info"
                                        color="blue"
                                        circular
                                        inverted
                                        size="tiny"
                                        style={{
                                          marginLeft: "6px",
                                          marginBottom: "2px",
                                          verticalAlign: "bottom",
                                          cursor: "pointer",
                                        }}
                                      />
                                    }
                                  >
                                    {item.description}
                                  </Popup>
                                )}
                              </Table.Cell>
                              <Table.Cell>{item.days}</Table.Cell>
                              <Table.Cell>{item.beforeOnAfter}</Table.Cell>
                              <Table.Cell>{item.dateType}</Table.Cell>
                              <Table.Cell>
                                {getAssignedToDisplayText(item.assignedTo)}
                              </Table.Cell>
                              <Table.Cell>
                                <TaskTemplatesItemActionButtons
                                  item={item}
                                  index={index}
                                  setInitialValues={setInitialValues}
                                  setItemModal={setItemModal}
                                />
                              </Table.Cell>
                            </>
                          </Table.Row>
                        ))}
                    </Table.Body>
                  </Table>
                  <Button
                    type="button"
                    className="medium top margin"
                    primary
                    onClick={() => handleAddItem()}
                    content="Add Task"
                  />
                  <Divider className="medium top margin" />
                  <Button
                    floated={isMobile ? null : "right"}
                    primary
                    type="submit"
                    content="Submit"
                    className={isMobile ? "fluid medium bottom margin" : null}
                  />
                  <Button
                    type="button"
                    onClick={() =>
                      dispatch(
                        closeModal({
                          modalType: "TaskTemplatesEdit",
                        })
                      )
                    }
                    to="#"
                    floated={isMobile ? null : "right"}
                    content="Cancel"
                    className={isMobile ? "fluid medium bottom margin" : null}
                  />
                </Form>
              )}
            </Formik>
          </div>
        </Segment>
      </ModalWrapper>
      <Modal
        closeIcon
        open={itemModal?.open}
        onClose={() => setItemModal({ open: false, new: null })}
        size="large"
      >
        <Modal.Content>
          {" "}
          <Segment clearing>
            <div className="medium horizontal margin small top margin">
              <Formik
                enableReinitialize
                initialValues={initialValues}
                validationSchema={validationSchema}
                validateOnChange={false}
                validateOnBlur={false}
                onSubmit={async (values, { setSubmitting }) => {
                  try {
                    itemModal.new
                      ? dispatch(addItemToTaskTemplate(values))
                      : dispatch(
                          updateItemInTaskTemplate({
                            index: itemModal.editingId,
                            item: values,
                          })
                        );
                    setSubmitting(false);
                    setItemModal({ open: false, new: null });
                    toast.success("Task successfully updated");
                  } catch (error) {
                    toast.error(error.message);
                    setSubmitting(false);
                  }
                }}
              >
                {({ isSubmitting, dirty, isValid, values }) => (
                  <Form className="ui form" autoComplete="off">
                    <Header size="large" color="blue">
                      {itemModal?.new ? "Create Task" : "Edit Task"}
                    </Header>
                    <Divider />
                    <Grid>
                      <Grid.Row className="small vertical padding">
                        <Grid.Column mobile={16} computer={6}>
                          <MyTextInput name="name" label="Name" />
                        </Grid.Column>
                        <Grid.Column mobile={16} computer={10}>
                          <MyTextArea
                            rows={4}
                            name="description"
                            label="Description"
                          />
                        </Grid.Column>
                      </Grid.Row>
                      <Grid.Row className="small vertical padding">
                        <Grid.Column mobile={16} computer={2}>
                          <MyNumberInput name="days" label="Days" />
                        </Grid.Column>
                        <Grid.Column mobile={16} computer={4}>
                          <MySelectInput
                            name="beforeOnAfter"
                            label="Before/On/After"
                            placeholder="Select time reference"
                            options={[
                              {
                                key: "Before",
                                text: "Before",
                                value: "Before",
                              },
                              { key: "On", text: "On", value: "On" },
                              { key: "After", text: "After", value: "After" },
                            ]}
                          />
                        </Grid.Column>

                        <Grid.Column mobile={16} computer={4}>
                          <MySelectInput
                            name="dateType"
                            label="Reference Date"
                            placeholder="Select reference date"
                            options={[
                              {
                                key: "",
                                text: "",
                                value: "",
                              },
                              {
                                key: "Contract Date",
                                text: "Contract Date",
                                value: "Contract Date",
                              },
                              {
                                key: "Closing Date",
                                text: "Closing Date",
                                value: "Closing Date",
                              },
                            ]}
                          />
                        </Grid.Column>
                      </Grid.Row>
                      <Grid.Row>
                        <Grid.Column mobile={16} computer={6}>
                          <MySelectInput
                            name="assignedTo"
                            label="Assign To"
                            placeholder="Select assignees"
                            options={assigneeOptions}
                            multiple={true}
                          />
                        </Grid.Column>
                        <Grid.Column mobile={16} computer={6}>
                          <MySelectInput
                            name="visibleTo"
                            label="Visible To (leave empty for everyone)"
                            options={assigneeOptions}
                            multiple={true}
                          />
                        </Grid.Column>
                      </Grid.Row>
                    </Grid>
                    <Divider className="medium top margin" />
                    <Button
                      loading={isSubmitting}
                      disabled={!dirty || isSubmitting}
                      type="submit"
                      floated={isMobile ? null : "right"}
                      primary
                      content={itemModal?.new ? "Add" : "Save"}
                      className={isMobile ? "fluid medium bottom margin" : null}
                    />
                    <Button
                      disabled={isSubmitting}
                      onClick={() => setItemModal({ open: false, new: null })}
                      to="#"
                      type="button"
                      floated={isMobile ? null : "right"}
                      content="Cancel"
                      className={isMobile ? "fluid medium bottom margin" : null}
                    />
                  </Form>
                )}
              </Formik>
            </div>
          </Segment>
        </Modal.Content>
      </Modal>
    </>
  );
}
